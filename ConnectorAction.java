/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import io.micrometer.core.instrument.Counter;

public class ConnectorAction {
    String actionType;
    ConnectorConfiguration configuration;
    Connector connector;
    HttpClientUtil httpClient;
    Counter actionCounter;
    Counter actionErrorCounter;
    // actionConfig is not always initialied
    ActionConfiguration actionConfig = null;

    public ConnectorAction(String actionType, ConnectorConfiguration configuration, HttpClientUtil httpClient,
            Connector connector, Counter actionCounter, Counter actionErrorCounter) {
        this.actionType = actionType;
        this.configuration = configuration;
        this.httpClient = httpClient;
        this.connector = connector;
        this.actionCounter = actionCounter;
        this.actionErrorCounter = actionErrorCounter;
    }

    public ConnectorAction(String actionType, ConnectorConfiguration configuration, ActionConfiguration actionConfig,
            HttpClientUtil httpClient, Connector connector, Counter actionCounter, Counter actionErrorCounter) {
        this.actionType = actionType;
        this.configuration = configuration;
        this.httpClient = httpClient;
        this.connector = connector;
        this.actionCounter = actionCounter;
        this.actionErrorCounter = actionErrorCounter;
        this.actionConfig = actionConfig;
    }

    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("Action: " + actionType + " with configuration: " + configuration.toString());

        return sb.toString();
    }

    public String getActionType() {
        return actionType;
    }

    public ConnectorConfiguration getConfiguration() {
        return configuration;
    }

    public HttpClientUtil getHttpClient() {
        return httpClient;
    }

    public Connector getConnector() {
        return connector;
    }

    public Counter getActionCounter() {
        return actionCounter;
    }

    public Counter getActionErrorCounter() {
        return actionErrorCounter;
    }

    public ActionConfiguration getActionConfiguration() {
        return actionConfig;
    }
}