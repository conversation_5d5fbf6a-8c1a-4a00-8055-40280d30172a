/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
// todo this is a template
package com.ibm.watson.aiops.connectors.snow;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ibm.aiops.connectors.bridge.ConnectorStatus;
import com.ibm.cp4waiops.connectors.sdk.ConnectorBase;
import com.ibm.cp4waiops.connectors.sdk.ConnectorConfigurationHelper;
import com.ibm.cp4waiops.connectors.sdk.Constant;
import com.ibm.cp4waiops.connectors.sdk.ConnectorException;
import com.ibm.cp4waiops.connectors.sdk.ConnectorManagerMicroprofileConfig;
import com.ibm.cp4waiops.connectors.sdk.EventLifeCycleEvent;
import com.ibm.cp4waiops.connectors.sdk.SDKSettings;
import com.ibm.cp4waiops.connectors.sdk.TruststoreCertificateException;
import com.ibm.cp4waiops.connectors.sdk.TruststoreCertificateHelper;
import com.ibm.cp4waiops.connectors.sdk.Util;

import io.cloudevents.CloudEvent;
import io.cloudevents.core.builder.CloudEventBuilder;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

public class Connector extends ConnectorBase {
    static final Logger logger = Logger.getLogger(Connector.class.getName());

    // Connector specific cloud event types
    static final String TEST_REQUESTED_CE_TYPE = "com.ibm.watson.aiops.connectors.template.test-requested";
    static final String TEST_COMPLETED_CE_TYPE = "com.ibm.watson.aiops.connectors.template.test-completed";
    static final String TEST_FAILED_CE_TYPE = "com.ibm.watson.aiops.connectors.template.test-failed";

    // Connector specific cloud event attributes
    static final String ADDRESS_CE_EXT = "address";
    static final String RESPONSE_TIME_CE_EXT = "responsetime";

    static final String ADDRESS_CE = "http://example.com/myaddress";

    // Self identifier
    static final URI SELF_SOURCE = URI.create("template.connectors.aiops.watson.ibm.com/connectorsnow");

    protected ConcurrentLinkedQueue<ConnectorAction> actionQueue = new ConcurrentLinkedQueue<ConnectorAction>();

    private AtomicReference<HttpClientUtil> httpClient = new AtomicReference<>();

    protected ThreadPoolExecutor executor = (ThreadPoolExecutor) Executors.newFixedThreadPool(2);

    private AtomicReference<ConnectorConfiguration> _configuration = new AtomicReference<>();
    private AtomicBoolean _configured = new AtomicBoolean(false);
    private AtomicInteger _status = new AtomicInteger(ConnectorStatus.Phase.Running_VALUE);

    private Counter _incidentCreateActionCounter;
    private Counter _incidentCreateActionErrorCounter;

    private Counter _alertUpdateActionCounter;
    private Counter _alertUpdateActionErrorCounter;

    private Counter _commentCreateActionCounter;
    private Counter _commentCreateActionErrorCounter;

    private Counter _incidentPollActionCounter;
    private Counter _incidentPollActionErrorCounter;

    private Counter _alertPollActionCounter;
    private Counter _alertPollActionErrorCounter;

    private Counter _changeRequestPollActionCounter;
    private Counter _changeRequestPollActionErrorCounter;

    private ConnectorAction incidentPollAction;
    private IncidentPollAction incidentPollActionInstance;

    private ConnectorAction alertPollAction;
    private AlertPollAction alertPollActionInstance;

    private ConnectorAction changeRequestPollAction;
    private ChangeRequestPollAction changeRequestPollActionInstance;

    static private String previousPassword = null;

    protected int sleepInterval = 1;
    protected long timeLastCleared = 0L;
    private String trustStorePass = null;

    private TruststoreCertificateHelper truststoreCertificateHelper = new TruststoreCertificateHelper();

    /**
     * Instantiates a new ConnectorTemplate
     */
    public Connector() {
        logger.info("Release version 7.2.0");
    }

    @Override
    public void registerMetrics(MeterRegistry metricRegistry) {
        super.registerMetrics(metricRegistry);

        _incidentPollActionCounter = metricRegistry.counter(ConnectorConstants.ACTION_POLL_COUNTER);
        _incidentPollActionErrorCounter = metricRegistry.counter(ConnectorConstants.ACTION_POLL_ERROR_COUNTER);

        _incidentCreateActionCounter = metricRegistry.counter(ConnectorConstants.ACTION_INCIDENT_CREATE_COUNTER);
        _incidentCreateActionErrorCounter = metricRegistry
                .counter(ConnectorConstants.ACTION_INCIDENT_CREATE_ERROR_COUNTER);

        _alertUpdateActionCounter = metricRegistry.counter(ConnectorConstants.ACTION_ALERT_UPDATE_COUNTER);
        _alertUpdateActionErrorCounter = metricRegistry.counter(ConnectorConstants.ACTION_ALERT_UPDATE_ERROR_COUNTER);

        _commentCreateActionCounter = metricRegistry.counter(ConnectorConstants.ACTION_COMMENT_CREATE_COUNTER);
        _commentCreateActionErrorCounter = metricRegistry
                .counter(ConnectorConstants.ACTION_COMMENT_CREATE_ERROR_COUNTER);

        _incidentPollActionCounter = metricRegistry.counter(ConnectorConstants.ACTION_INCIDENT_POLL_COUNTER);
        _incidentPollActionErrorCounter = metricRegistry.counter(ConnectorConstants.ACTION_INCIDENT_POLL_ERROR_COUNTER);

        _alertPollActionCounter = metricRegistry.counter(ConnectorConstants.ACTION_ALERT_POLL_COUNTER);
        _alertPollActionErrorCounter = metricRegistry.counter(ConnectorConstants.ACTION_ALERT_POLL_ERROR_COUNTER);

        _changeRequestPollActionCounter = metricRegistry.counter(ConnectorConstants.ACTION_CHANGE_REQUEST_POLL_COUNTER);
        _changeRequestPollActionErrorCounter = metricRegistry
                .counter(ConnectorConstants.ACTION_CHANGE_REQUEST_POLL_ERROR_COUNTER);
    }

    @Override
    public SDKSettings onConfigure(CloudEvent event) throws ConnectorException {

        logger.log(Level.INFO, "onConfigure");
        _status.set(ConnectorStatus.Phase.Running_VALUE);

        ConnectorConfiguration connectionCreateCfg = new ConnectorConfiguration();

        ConnectorConfigurationHelper helper = new ConnectorConfigurationHelper(event);

        connectionCreateCfg.loadDataFromJson(Util.convertCloudEventToJSON(event));

        // Added for debugging https://github.ibm.com/katamari/dev-issue-tracking/issues/32421
        logger.log(Level.INFO, "Username: " + connectionCreateCfg.getUsername());
        String password = connectionCreateCfg.getPassword();
        if (password != null) {
            if (password.equals(previousPassword)) {
                logger.log(Level.INFO, "Password did not change.");
            } else {
                logger.log(Level.INFO, "Password changed.");
            }
        }
        previousPassword = password;

        setTruststorePass(helper);

        buildHttpClient(_configuration.get(), connectionCreateCfg);

        // TODO: remove this temporary workaround
        if (!_configured.get() || hasConnectionCreateCfgChanged(_configuration.get(), connectionCreateCfg)) {
            collectData(connectionCreateCfg);
            _configured.set(true);
        }

        // Update succeeded, store accepted configuration
        _configuration.set(connectionCreateCfg);

        // Return topics
        SDKSettings settings = new SDKSettings();
        settings.consumeTopicNames = new String[] { ConnectorConstants.TOPIC_INPUT_ACTION_SNOW,
                ConnectorConstants.TOPIC_INPUT_SNOW_HANDLERS };
        // Note: ACTION_1_TOPIC is consumed from only for demonstration purposes, avoid
        // consuming from the same topic produced to in real connectors!
        settings.produceTopicNames = new String[] { ConnectorConstants.TOPIC_OUTPUT_ACTION_SNOW,
                ConnectorConstants.TOPIC_OUTPUT_CHANGE_REQUEST, ConnectorConstants.TOPIC_OUTPUT_INCIDENT,
                ConnectorConstants.TOPIC_OUTPUT_SNOW_ALERT, ConnectorConstants.TOPIC_OUTPUT_ITSM_INCIDENT_RESPONSE,
                ConnectorConstants.TOPIC_INPUT_LIFECYCLE_EVENTS };

        logger.log(Level.INFO, "onConfigure results: " + connectionCreateCfg.toString());

        return settings;
    }

    protected boolean isTypeIncluded(String types, String type) {
        if (types == null || types.isBlank()) {
            // Types is empty, so all types are used
            return true;
        } else if (types != null && !types.isBlank() && types.contains(type)) {
            // Types is not empty
            return true;
        }
        return false;
    }

    void collectData(ConnectorConfiguration connectionCreateCfg) {
        ActionUtils.getAndSetConfigTimeZone(httpClient.get(), this, connectionCreateCfg);

        logger.log(Level.INFO, "collectData(): Stopping existing polling");

        // Stop polling
        if (incidentPollActionInstance != null) {
            logger.log(Level.INFO, "collectData(): stopping incident polling");
            incidentPollActionInstance.stop();
        }

        if (changeRequestPollActionInstance != null) {
            logger.log(Level.INFO, "collectData(): stopping change request polling");
            changeRequestPollActionInstance.stop();
        }

        if (alertPollActionInstance != null) {
            logger.log(Level.INFO, "collectData(): stopping alert polling");
            alertPollActionInstance.stop();
        }

        // collect data if data flow is enable
        if (connectionCreateCfg.getRequestAction() != null
                && connectionCreateCfg.getRequestAction().equals(ConnectorConstants.REQUEST_ACTION_DATA_FLOW_ON)) {
            logger.log(Level.INFO, "Data flow is on");

            // incident polling for incident sync, similar incidents, or both, as well as historic polling
            if (connectionCreateCfg.getIncidentSyncToggle() || connectionCreateCfg.getSimilarIncidentToggle()
                    || (ConnectorConstants.HISTORICAL.equals(connectionCreateCfg.getConnMode())
                            && isTypeIncluded(connectionCreateCfg.getTypes(), ConnectorConstants.INCIDENT))) {
                incidentPollAction = new ConnectorAction(ConnectorConstants.INCIDENT_POLL, connectionCreateCfg,
                        httpClient.get(), this, _incidentPollActionCounter, _incidentPollActionErrorCounter);
                addActionToQueue(incidentPollAction);
            }
            if (connectionCreateCfg.getAlertSyncToggle()) {
                alertPollAction = new ConnectorAction(ConnectorConstants.ALERT_POLL, connectionCreateCfg,
                        httpClient.get(), this, _alertPollActionCounter, _alertPollActionErrorCounter);
                addActionToQueue(alertPollAction);
            }

            if (connectionCreateCfg.getChangeRequestToggle()
                    || (ConnectorConstants.HISTORICAL.equals(connectionCreateCfg.getConnMode())
                            && isTypeIncluded(connectionCreateCfg.getTypes(), ConnectorConstants.CHANGE_REQUEST))) {
                changeRequestPollAction = new ConnectorAction(ConnectorConstants.CHANGE_REQUEST_POLL,
                        connectionCreateCfg, httpClient.get(), this, _changeRequestPollActionCounter,
                        _changeRequestPollActionErrorCounter);
                addActionToQueue(changeRequestPollAction);
            }
        }
    }

    protected boolean hasConnectionCreateCfgChanged(ConnectorConfiguration oldConfig,
            ConnectorConfiguration newConfig) {
        if (oldConfig != null && stringsEqual(oldConfig.getRequestAction(), newConfig.getRequestAction())
                && stringsEqual(oldConfig.getStartdate(), newConfig.getStartdate())
                && stringsEqual(oldConfig.getConnMode(), newConfig.getConnMode())
                && stringsEqual(oldConfig.getEnddate(), newConfig.getEnddate())
                && stringsEqual(oldConfig.getURL(), newConfig.getURL())
                && oldConfig.getSimilarIncidentToggle() == newConfig.getSimilarIncidentToggle()
                && oldConfig.getSimilarIncidentInterval() == newConfig.getSimilarIncidentInterval()
                && stringsEqual(oldConfig.getSimilarIncidentIntervalUnit(), newConfig.getSimilarIncidentIntervalUnit())
                && oldConfig.getAlertSyncToggle() == newConfig.getAlertSyncToggle()
                && oldConfig.getAlertSyncInterval() == newConfig.getAlertSyncInterval()
                && stringsEqual(oldConfig.getAlertSyncIntervalUnit(), newConfig.getAlertSyncIntervalUnit())
                && oldConfig.getIncidentSyncToggle() == newConfig.getIncidentSyncToggle()
                && oldConfig.getIncidentSyncInterval() == newConfig.getIncidentSyncInterval()
                && stringsEqual(oldConfig.getIncidentSyncIntervalUnit(), newConfig.getIncidentSyncIntervalUnit())
                && oldConfig.getChangeRequestToggle() == newConfig.getChangeRequestToggle()
                && oldConfig.getChangeRequestInterval() == newConfig.getChangeRequestInterval()
                && stringsEqual(oldConfig.getChangeRequestIntervalUnit(), newConfig.getChangeRequestIntervalUnit())
                && stringsEqual(oldConfig.getUsername(), newConfig.getUsername())
                && stringsEqual(oldConfig.getPassword(), newConfig.getPassword())) {
            logger.log(Level.INFO, "hasConnectionCreateCfgChanged(): configuration has not changed");
            return false;
        }
        logger.log(Level.INFO, "hasConnectionCreateCfgChanged(): configuration has changed");
        return true;
    }

    protected void setTruststorePass(ConnectorConfigurationHelper helper) {

        ConnectorManagerMicroprofileConfig mpConfig = new ConnectorManagerMicroprofileConfig();

        setTrustStorePassword(mpConfig.getConnectorClientSecret(helper.getSystemName()));

    }

    protected void buildHttpClient(ConnectorConfiguration oldConfig, ConnectorConfiguration newConfig)
            throws ConnectorException {
        // Skip if no change was made
        if (httpClient.get() != null && oldConfig != null && stringsEqual(oldConfig.getURL(), newConfig.getURL())
                && stringsEqual(oldConfig.getUsername(), newConfig.getUsername())
                && stringsEqual(oldConfig.getPassword(), newConfig.getPassword())
                && oldConfig.getTls() == newConfig.getTls()
                && stringsEqual(oldConfig.getCertificate(), newConfig.getCertificate())
                && oldConfig.getIsProxyEnabled() == newConfig.getIsProxyEnabled()
                && stringsEqual(oldConfig.getProxyUrl(), newConfig.getProxyUrl())
                && oldConfig.getProxyPort() == newConfig.getProxyPort()
                && stringsEqual(oldConfig.getProxyUsername(), newConfig.getProxyUsername())
                && stringsEqual(oldConfig.getProxyPassword(), newConfig.getProxyPassword())) {
            return;
        }

        logger.log(Level.INFO, "Building http client");

        // Setuo SSL & Proxy for connector
        try {

            truststoreCertificateHelper.importTrustStoreCert(newConfig.getTls(), newConfig.getCertificate(),
                    this.trustStorePass);

            HttpClientUtil client = new HttpClientUtil(newConfig.getURL(), newConfig.getUsername(),
                    newConfig.getPassword());

            client.setupSSLAndProxy(newConfig, this.trustStorePass);

            httpClient.set(client);

            logger.log(Level.INFO, "Http client created");

        } catch (KeyManagementException | NoSuchAlgorithmException e) {
            logger.log(Level.SEVERE, "Failed to setup SSL for connector : " + e.getMessage());
            throw new TruststoreCertificateException(
                    "Failed to setup SSL Certificate for connector : " + e.getMessage());
        } catch (KeyStoreException | CertificateException | IOException | UnrecoverableKeyException e) {
            logger.log(Level.SEVERE, "Failed to setup SSL for connector : " + e.getMessage());
            throw new TruststoreCertificateException(
                    "Failed to setup SSL Certificate for connector : " + e.getMessage());
        } catch (Exception error) {
            throw new ConnectorException("Failed to client http client", error);
        }

    }

    @Override
    public SDKSettings onReconfigure(CloudEvent event) throws ConnectorException {
        // Update topics and local state if needed
        return onConfigure(event);
    }

    @Override
    public void onTerminate(CloudEvent event) {
        // Clear alerts
        logger.log(Level.INFO, "onTerminate, clearing alerts");
        ConnectorConfiguration connectiondeleteCfg = new ConnectorConfiguration();
        connectiondeleteCfg.loadDataFromJson(Util.convertCloudEventToJSON(event));
        clearAlerts(connectiondeleteCfg, ConnectorConstants.ALERT_TYPES_LIST, 0L);
        logger.log(Level.INFO, "Alerts cleared");

    }

    @Override
    public void onAction(String channelName, CloudEvent event) {
        logger.log(Level.INFO, "onAction with type " + event.getType());
        ConnectorConfiguration connectorCfg = _configuration.get();

        // If an action comes in before the configuration is setup, skip the action
        if (connectorCfg != null) {
            ActionConfiguration actionCfg;
            switch (event.getType()) {
            case ConnectorConstants.INCIDENT_CREATE:
                actionCfg = new ActionConfiguration();
                actionCfg.loadDataFromJson(Util.convertCloudEventToJSON(event));

                ConnectorAction incidentCreateAction = new ConnectorAction(ConnectorConstants.INCIDENT_CREATE,
                        connectorCfg, actionCfg, httpClient.get(), this, _incidentCreateActionCounter,
                        _incidentCreateActionErrorCounter);
                addActionToQueue(incidentCreateAction);
                break;
            case ConnectorConstants.COMMENT_CREATE:
                actionCfg = new ActionConfiguration();
                actionCfg.loadDataFromJson(Util.convertCloudEventToJSON(event));

                ConnectorAction commentCreateAction = new ConnectorAction(ConnectorConstants.COMMENT_CREATE,
                        connectorCfg, actionCfg, httpClient.get(), this, _commentCreateActionCounter,
                        _commentCreateActionErrorCounter);
                addActionToQueue(commentCreateAction);
                break;
            case ConnectorConstants.ALERT_UPDATE:
                actionCfg = new ActionConfiguration();
                actionCfg.loadDataFromJson(Util.convertCloudEventToJSON(event));

                ConnectorAction alertUpdateAction = new ConnectorAction(ConnectorConstants.ALERT_UPDATE, connectorCfg,
                        actionCfg, httpClient.get(), this, _alertUpdateActionCounter, _alertUpdateActionErrorCounter);
                addActionToQueue(alertUpdateAction);
                break;
            }
        } else {
            logger.log(Level.INFO, "Skipping action since connector configuration is empty");
        }
    }

    @Override
    public void run() {
        boolean interrupted = false;

        long lastStatusUpdate = 0;
        while (!interrupted) {
            try {
                // Process next action
                processNextAction();

                // Periodic status update
                if (System.nanoTime() - lastStatusUpdate > Duration.ofMinutes(5).toNanos()) {
                    lastStatusUpdate = System.nanoTime();
                    emitStatus(ConnectorStatus.Phase.forNumber(_status.get()), Duration.ofMinutes(5));
                }

                // Can remove this sleep after...
                Thread.sleep(100);

            } catch (InterruptedException ignored) {
                // termination of the process has been requested
                interrupted = true;
                Thread.currentThread().interrupt();
            }
        }
    }

    protected void processNextAction() {
        ConnectorAction currentAction = actionQueue.poll();
        if (currentAction != null) {
            logger.log(Level.INFO, currentAction.toString());
            Runnable action = ConnectorActionFactory.getRunnableAction(currentAction);
            if (action instanceof IncidentPollAction) {
                incidentPollActionInstance = (IncidentPollAction) action;
            } else if (action instanceof ChangeRequestPollAction) {
                changeRequestPollActionInstance = (ChangeRequestPollAction) action;
            } else if (action instanceof AlertPollAction) {
                alertPollActionInstance = (AlertPollAction) action;
            }
            executor.execute(action);
        }
    }

    public void triggerAlerts(ConnectorConfiguration config, Exception e, String message) {
        CloudEvent ce;
        String errorMsg = "";
        if (e.getMessage() != null) {
            errorMsg = e.getMessage();
        }
        if (message == null) {
            message = "";
        }
        if (message.contains("instance-hibernating-page") || errorMsg.contains("instance-hibernating-page")) {
            try {
                ce = createAlertEvent(config, ConnectorConstants.INSTANCE_HIBERNATING_CE_TYPE,
                        EventLifeCycleEvent.EVENT_TYPE_PROBLEM);
                emitCloudEvent(ConnectorConstants.TOPIC_INPUT_LIFECYCLE_EVENTS, getPartition(), ce);
                logger.log(Level.INFO,
                        "Alert created: Your instance is hibernating, sleeping for " + sleepInterval + " minute");
                TimeUnit.MINUTES.sleep(sleepInterval);
            } catch (JsonProcessingException | InterruptedException e1) {
                logger.log(Level.SEVERE, e1.getMessage(), e1);
            }
        } else if (message.contains("instance-offline-page") || errorMsg.contains("instance-offline-page")) {
            try {
                ce = createAlertEvent(config, ConnectorConstants.INSTANCE_OFFLINE_CE_TYPE,
                        EventLifeCycleEvent.EVENT_TYPE_PROBLEM);
                emitCloudEvent(ConnectorConstants.TOPIC_INPUT_LIFECYCLE_EVENTS, getPartition(), ce);
                logger.log(Level.INFO, "Alert created: Instance Offline, sleeping for " + sleepInterval + " minute");
                TimeUnit.MINUTES.sleep(sleepInterval);
            } catch (JsonProcessingException | InterruptedException e1) {
                logger.log(Level.SEVERE, e1.getMessage(), e1);
            }
        } else if (message.contains("User Not Authenticated") || errorMsg.contains("User Not Authenticated")) {
            try {
                _status.set(ConnectorStatus.Phase.Errored_VALUE);
                ce = createAlertEvent(config, ConnectorConstants.INSTANCE_UNAUTHENTICATED_CE_TYPE,
                        EventLifeCycleEvent.EVENT_TYPE_PROBLEM);
                emitCloudEvent(ConnectorConstants.TOPIC_INPUT_LIFECYCLE_EVENTS, getPartition(), ce);
                logger.log(Level.INFO,
                        "Alert created: User Not Authenticated, sleeping for " + sleepInterval + " minute");
                TimeUnit.MINUTES.sleep(sleepInterval);
            } catch (JsonProcessingException | InterruptedException e1) {
                logger.log(Level.SEVERE, e1.getMessage(), e1);
            }
        } else if (message.contains("Requested URI does not represent any resource")
                || errorMsg.contains("Requested URI does not represent any resource")) {
            try {
                _status.set(ConnectorStatus.Phase.Errored_VALUE);
                ce = createAlertEvent(config, ConnectorConstants.SNOW_APP_MISSING_CE_TYPE,
                        EventLifeCycleEvent.EVENT_TYPE_PROBLEM);
                emitCloudEvent(ConnectorConstants.TOPIC_INPUT_LIFECYCLE_EVENTS, getPartition(), ce);
                logger.log(Level.INFO, "Alert created: Requested URI does not represent any resource, sleeping for "
                        + sleepInterval + " minute");
                TimeUnit.MINUTES.sleep(sleepInterval);
            } catch (JsonProcessingException | InterruptedException e1) {
                logger.log(Level.SEVERE, e1.getMessage(), e1);
            }
        } else {
            logger.log(Level.SEVERE, "Unable to create alert, Error message: " + message + errorMsg);
        }

    }

    public void clearAlerts(ConnectorConfiguration config, List<String> alertTypes, Long time) {
        CloudEvent ce;
        if (System.currentTimeMillis() - time > 60000) {
            for (String alertType : alertTypes) {
                try {
                    ce = createAlertEvent(config, alertType, EventLifeCycleEvent.EVENT_TYPE_RESOLUTION);
                    emitCloudEvent(ConnectorConstants.TOPIC_INPUT_LIFECYCLE_EVENTS, getPartition(), ce);
                } catch (JsonProcessingException e) {
                    logger.log(Level.SEVERE, e.getMessage(), e);
                }
            }
            setTimeLastCleared(System.currentTimeMillis());
        }
    }

    public CloudEvent createEvent(long responseTime, String ce_type, String jsonMessage, URI source) {
        // Default source in case none is set
        if (source == null) {
            source = SELF_SOURCE;
        }

        // The cloud event being returned needs to be in a structured format
        return CloudEventBuilder.v1().withId(UUID.randomUUID().toString()).withSource(source)
                .withTime(OffsetDateTime.now()).withType(ce_type).withExtension(RESPONSE_TIME_CE_EXT, responseTime)
                .withExtension(CONNECTION_ID_CE_EXTENSION_NAME, getConnectorID())
                .withExtension(COMPONENT_NAME_CE_EXTENSION_NAME, getComponentName())
                .withExtension(ConnectorConstants.TOOL_TYPE, ConnectorConstants.TOOL_TYPE_SNOW)
                .withExtension(ConnectorConstants.CE_EXT_STRUCTURED_CONTENT_MODE,
                        ConnectorConstants.CE_EXT_STRUCTURED_CONTENT_MODE_VALUE)
                .withData("application/json", jsonMessage.getBytes()).build();
    }

    public CloudEvent createAlertEvent(ConnectorConfiguration config, String alertType, String eventType)
            throws JsonProcessingException {
        EventLifeCycleEvent elcEvent = newInstanceAlertEvent(config, alertType, eventType);
        return CloudEventBuilder.v1().withId(elcEvent.getId()).withSource(SELF_SOURCE).withType(alertType)
                .withExtension(TENANTID_TYPE_CE_EXTENSION_NAME, Constant.STANDARD_TENANT_ID)
                .withExtension(CONNECTION_ID_CE_EXTENSION_NAME, getConnectorID())
                .withExtension(COMPONENT_NAME_CE_EXTENSION_NAME, getComponentName())
                .withData(Constant.JSON_CONTENT_TYPE, elcEvent.toJSON().getBytes(StandardCharsets.UTF_8)).build();
    }

    protected static EventLifeCycleEvent newInstanceAlertEvent(ConnectorConfiguration config, String alertType,
            String eventType) {

        EventLifeCycleEvent event = new EventLifeCycleEvent();
        EventLifeCycleEvent.Type type = new EventLifeCycleEvent.Type();
        Map<String, String> details = new HashMap<>();

        Map<String, Object> sender = new HashMap<>();
        sender.put(EventLifeCycleEvent.RESOURCE_TYPE_FIELD, "ServiceNow connection");
        sender.put(EventLifeCycleEvent.RESOURCE_NAME_FIELD, config.getComponentName());
        sender.put(EventLifeCycleEvent.RESOURCE_SOURCE_ID_FIELD, config.getConnectionID());
        event.setSender(sender);

        Map<String, Object> resource = new HashMap<>();
        resource.put(EventLifeCycleEvent.RESOURCE_TYPE_FIELD, "ServiceNow connection");
        resource.put(EventLifeCycleEvent.RESOURCE_NAME_FIELD, config.getComponentName());
        resource.put(EventLifeCycleEvent.RESOURCE_SOURCE_ID_FIELD, config.getConnectionID());
        event.setResource(resource);

        event.setId(UUID.randomUUID().toString());
        event.setOccurrenceTime(Date.from(Instant.now()));
        event.setSeverity(3);
        event.setExpirySeconds(0);

        type.setEventType(eventType);
        type.setClassification("Monitoring issue");

        if (alertType == ConnectorConstants.INSTANCE_HIBERNATING_CE_TYPE) {
            event.setSummary("ServiceNow instance hibernating");
            type.setCondition("ServiceNow Instance Hibernating");
            details.put("guidance", "Wake up Service Now instance from hibernation");
        } else if (alertType == ConnectorConstants.INSTANCE_OFFLINE_CE_TYPE) {
            event.setSummary("ServiceNow instance offline");
            type.setCondition("ServiceNow Instance Offline");
            details.put("guidance", "Ensure Service Now instance exists and is online");
        } else if (alertType == ConnectorConstants.INSTANCE_UNAUTHENTICATED_CE_TYPE) {
            event.setSummary("ServiceNow instance authentication failed");
            type.setCondition("ServiceNow Instance Unauthenticated");
            details.put("guidance", "Ensure Service Now credentials are valid");
        } else if (alertType == ConnectorConstants.SNOW_APP_MISSING_CE_TYPE) {
            event.setSummary("IBM Cloud Pak for Watson AIOps ServiceNow integration missing on instance");
            type.setCondition("IBM Cloud Pak for Watson AIOps ServiceNow integration missing");
            details.put("guidance",
                    "Ensure IBM Cloud Pak for Watson AIOps ServiceNow integration is installed on ServiceNow");
        }

        event.setType(type);
        event.setDetails(details);

        return event;
    }

    protected boolean stringsEqual(String a, String b) {
        if (a == null || b == null)
            return a == b;
        return a.equals(b);
    }

    // In the future this may be dynamically generated, so use a method to get the
    // value
    protected String getEventAddress() {
        return ADDRESS_CE;
    }

    // A helper function that checks github instantiation before adding action to
    // queue
    private void addActionToQueue(ConnectorAction action) {
        actionQueue.add(action);
        logger.log(Level.INFO, "Action was successfully added");
    }

    protected ConnectorConfiguration getConnectorConfiguration() {
        return _configuration.get();
    }

    protected ConcurrentLinkedQueue<ConnectorAction> getActionQueue() {
        return actionQueue;
    }

    protected String getPartition() {
        // Generate the partition
        ConnectorConfiguration currentConfig = getConnectorConfiguration();
        if (currentConfig != null) {
            String connectionID = currentConfig.getConnectionID();
            if (connectionID != null && !connectionID.isEmpty()) {
                return "{\"ce-partitionkey\":\"" + connectionID + "\"}";
            }
        }

        // If a partition cannot be created, return null
        // Null is a valid partition and will not throw errors, but
        // can run into unintended consequences from consumerss
        return null;
    }

    public int getSleepInterval() {
        return sleepInterval;
    }

    public void setSleepInterval(int sleepInterval) {
        this.sleepInterval = sleepInterval;
    }

    public long getTimeLastCleared() {
        return timeLastCleared;
    }

    public void setTimeLastCleared(long timeLastCleared) {
        this.timeLastCleared = timeLastCleared;
    }

    private void setTrustStorePassword(String trustStorePass) {
        this.trustStorePass = trustStorePass;
    }

    private String getTrustStorePassword() {
        return this.trustStorePass;
    }
}
