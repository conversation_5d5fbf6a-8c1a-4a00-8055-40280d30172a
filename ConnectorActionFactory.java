/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

public class ConnectorActionFactory {
    public static Runnable getRunnableAction(ConnectorAction action) {
        if (action != null) {
            if (ConnectorConstants.INCIDENT_CREATE.equals(action.getActionType())) {
                return new IncidentCreateAction(action);
            } else if (ConnectorConstants.INCIDENT_POLL.equals(action.getActionType())) {
                return new IncidentPollAction(action);
            } else if (ConnectorConstants.CHANGE_REQUEST_POLL.equals(action.getActionType())) {
                return new ChangeRequestPollAction(action);
            } else if (ConnectorConstants.COMMENT_CREATE.equals(action.getActionType())) {
                return new CommentCreateAction(action);
            } else if (ConnectorConstants.ALERT_UPDATE.equals(action.getActionType())) {
                return new AlertUpdateAction(action);
            } else if (ConnectorConstants.ALERT_POLL.equals(action.getActionType())) {
                return new AlertPollAction(action);
            }
        }
        return null;
    }
}
