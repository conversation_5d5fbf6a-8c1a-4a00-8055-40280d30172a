/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import java.net.http.HttpResponse;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import jakarta.json.Json;
import jakarta.json.JsonObject;

import org.json.JSONObject;

import io.cloudevents.CloudEvent;
import io.micrometer.core.instrument.Counter;

public class CommentCreateAction implements Runnable {

    private Counter actionCounter;
    private Counter actionErrorCounter;
    private ConnectorConfiguration config;
    private ActionConfiguration actionConfig;
    private HttpClientUtil httpClient;

    private Connector connector;
    private CloudEvent ce;

    static final Logger logger = Logger.getLogger(CommentCreateAction.class.getName());

    public CommentCreateAction(ConnectorAction action) {

        actionCounter = action.getActionCounter();
        actionErrorCounter = action.getActionErrorCounter();

        config = action.getConfiguration();
        actionConfig = action.getActionConfiguration();

        httpClient = action.getHttpClient();
        connector = action.getConnector();

        actionCounter = action.getActionCounter();
        actionErrorCounter = action.getActionErrorCounter();
    }

    @Override
    public void run() {
        logger.log(Level.INFO, "Run Comment Create Action");
        actionCounter.increment();

        JSONObject json = new JSONObject();

        String path = ConnectorConstants.BASIC_TABLE_API_PATH + actionConfig.getKind() + "/" + actionConfig.getSysID();

        CompletableFuture<HttpResponse<String>> cFResp;
        HttpResponse<String> res;
        String bodyString = "";

        JsonObject work_notes = Json.createObjectBuilder().add("work_notes", actionConfig.getBody()).build();

        try {
            logger.log(Level.INFO, "Adding comment to " + actionConfig.getKind() + actionConfig.getSysID());

            logger.log(Level.INFO, "Comment data " + actionConfig.getData());

            try {
                cFResp = httpClient.post(ConnectorConstants.CHANGE_RISK_ASSESSMENT, actionConfig.getData());
                logger.log(Level.INFO, "Endpoint check " + cFResp.get());
            } catch (Exception e) {
                logger.log(Level.INFO, "Couldn't reach change risk assessment endpoint " + e.getMessage());
                connector.triggerAlerts(config, e, e.getMessage());
            }
            // For 3.4, put comment in the new change risk assessment tab and into the
            // worknotes
            cFResp = httpClient.patch(path, work_notes.toString());

            logger.log(Level.INFO, "Comment creation successful ");

            res = cFResp.get();
            bodyString = res.body();

            json.put("connection_id", config.getConnectionID());
            json.put("snow_response", bodyString);
            json.put("kind", actionConfig.getKind());
            json.put("sys_id", actionConfig.getSysID());
            json.put("work_notes", actionConfig.getBody());

            // Using Connector.SELF_SOURCE as CP4WAIOps 3.2.1 did not specify a source for
            // comment creation
            ce = connector.createEvent((System.nanoTime() - config.getStartTime()),
                    ConnectorConstants.COMMENT_CREATE_COMPLETED, json.toString(), Connector.SELF_SOURCE);
            connector.emitCloudEvent(ConnectorConstants.TOPIC_OUTPUT_ACTION_SNOW, connector.getPartition(), ce);

        } catch (InterruptedException e) {
            actionErrorCounter.increment();
            logger.log(Level.SEVERE, "Comment creation failed ", e);
        } catch (Exception e) {
            actionErrorCounter.increment();
            logger.log(Level.SEVERE, e.getMessage(), e);
            connector.triggerAlerts(config, e, bodyString);
        }
        connector.clearAlerts(config, ConnectorConstants.ALL_ALERTS_MINUS_IBM_APP, connector.getTimeLastCleared());

    }

}
