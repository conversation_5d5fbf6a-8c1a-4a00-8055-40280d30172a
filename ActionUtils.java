/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.json.JSONArray;
import org.json.JSONObject;

public class ActionUtils {

    static final Logger logger = Logger.getLogger(ActionUtils.class.getName());

    static String encodeString(String string) {
        String encodedString = string;
        try {
            encodedString = URLEncoder.encode(string, "UTF-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            logger.log(Level.SEVERE, e.getMessage(), e);
        }
        return encodedString;
    }

    static String formatDate(ZonedDateTime date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("''yyyy-MM-dd'',''HH:mm:ss''");
        return "javascript:gs.dateGenerate(" + date.format(formatter) + ")";
    }

    static String convertTimezones(String date, String timeZone) {

        ZonedDateTime dateObj = ZonedDateTime.parse(date);
        ZoneId id = ZoneId.of(timeZone);
        ZonedDateTime convertedTime = dateObj.withZoneSameInstant(id);

        return formatDate(convertedTime);
    }

    static TimeUnit convertToTimeUnit(String unit) {
        switch (unit) {
        case "seconds":
            return TimeUnit.SECONDS;
        case "minutes":
            return TimeUnit.MINUTES;
        case "hours":
            return TimeUnit.HOURS;
        default:
            return TimeUnit.MINUTES;
        }
    }

    static int convertToSeconds(int interval, String unit) {
        switch (unit) {
        case "seconds":
            return interval;
        case "minutes":
            return interval * 60;
        case "hours":
            return interval * 3600;
        default:
            return interval * 60;
        }
    }

    // returns a string of endRaw minus the amount of time constructed with the interval & intervalUnit
    static String getStart(int interval, String intervalUnit, ZonedDateTime endRaw) {
        switch (intervalUnit) {
        case "seconds":
            return endRaw.minus(Duration.ofSeconds(interval)).toString();
        case "minutes":
            return endRaw.minus(Duration.ofMinutes(interval)).toString();
        case "hours":
            return endRaw.minus(Duration.ofHours(interval)).toString();
        default:
            return endRaw.minus(Duration.ofMinutes(interval)).toString();
        }
    }

    // returns the range of time on which to poll for
    static String getRange(ConnectorConfiguration config, String dataType) {
        // grab the specified timezone, default to PST
        String timeZone = config.getTimeZone();
        if (timeZone == null || timeZone.isBlank()) {
            timeZone = "America/Los_Angeles";
        }

        // default value for start is epoch
        String start = ZonedDateTime.ofInstant(Instant.EPOCH, ZoneId.of(timeZone)).toString();
        // default value for end is current time
        ZonedDateTime endRaw = ZonedDateTime.now(ZoneId.of(timeZone));
        String end = endRaw.toString();

        // live data polling
        if ((config.getStartdate() == null || config.getStartdate().isBlank())
                && (config.getEnddate() == null || config.getEnddate().isBlank())) {
            switch (dataType) {
            case ConnectorConfiguration.PROPERTY_SIMILAR_INCIDENT:
                int similarIncidentInterval = config.getSimilarIncidentInterval();
                String similarIncidentIntervalUnit = config.getSimilarIncidentIntervalUnit();
                start = getStart(similarIncidentInterval, similarIncidentIntervalUnit, endRaw);
                break;
            case ConnectorConfiguration.PROPERTY_INCIDENT_SYNC:
                int incidentSyncInterval = config.getIncidentSyncInterval();
                String incidentSyncIntervalUnit = config.getIncidentSyncIntervalUnit();
                start = getStart(incidentSyncInterval, incidentSyncIntervalUnit, endRaw);
                break;
            case ConnectorConfiguration.PROPERTY_ALERT_POLLING:
                int alertSyncInterval = config.getAlertSyncInterval();
                String alertSyncIntervalUnit = config.getAlertSyncIntervalUnit();
                start = getStart(alertSyncInterval, alertSyncIntervalUnit, endRaw);
                break;
            case ConnectorConfiguration.PROPERTY_CHANGE_REQUEST:
                int changeRequestInterval = config.getChangeRequestInterval();
                String changeRequestIntervalUnit = config.getChangeRequestIntervalUnit();
                start = getStart(changeRequestInterval, changeRequestIntervalUnit, endRaw);
                break;
            }
        }

        // historic poll
        if (config.getStartdate() != null && !config.getStartdate().isBlank()) {
            start = config.getStartdate();
        }
        if (config.getEnddate() != null && !config.getEnddate().isBlank()) {
            end = config.getEnddate();
        }

        return convertTimezones(start, timeZone) + "@" + convertTimezones(end, timeZone);
    }

    static void getAndSetConfigTimeZone(HttpClientUtil httpClient, Connector connector, ConnectorConfiguration config) {
        String bodyString = "";
        try {
            logger.log(Level.INFO, "Setting time zone... ");
            String path = "/api/now/table/sys_user?sysparm_query=user_name="
                    + ActionUtils.encodeString(config.getUsername()) + "&sysparm_fields=time_zone,date_format";
            CompletableFuture<HttpResponse<String>> cFResp = httpClient.get(path);
            HttpResponse<String> res = cFResp.get();
            bodyString = res.body();
            JSONObject body = new JSONObject(bodyString);
            JSONArray result = body.getJSONArray("result");
            String timeZone = result.getJSONObject(0).getString("time_zone");

            if (timeZone.length() > 0) {
                logger.log(Level.INFO, "Time zone set to " + timeZone);
                config.setTimeZone(timeZone);
            }

        } catch (InterruptedException e) {
            logger.log(Level.SEVERE, "Time zone query failed ", e);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Could not get response ", e);
            connector.triggerAlerts(config, e, bodyString);
        }

    }

    private static String getValue(JSONObject snowResource, String key) {
        Object value = snowResource.get(key);
        if (value == null)
            value = "";
        return value.toString();
    }

    static void applyMappings(JSONObject snowResource, JSONObject json, Map<String, String> mappings) {
        for (Entry<String, String> mapping : mappings.entrySet()) {

            String key = mapping.getKey();
            if (!json.has(key))
                continue;

            String value = getValue(snowResource, mapping.getValue());
            if (!value.isBlank())
                json.put(key, value);
        }
    }

    // Gets the source identifier
    // For example, to generate: https://dev109758.service-now.com/change_request.do?sysparm_query=number=CHG0030391
    // instanceURL = https://dev82395.service-now.com
    // path = /change_request.do?sysparm_query=number=
    // number = CHG0030391
    public static String getSourceIdentifier(String instanceURL, String path, String number) {
        if (number != null && number != "") {
            // Remove trailing slash if it exists
            if (instanceURL != null && path != null) {
                if (instanceURL.endsWith("/")) {
                    instanceURL = instanceURL.substring(0, instanceURL.length() - 1);
                }
                return instanceURL + path + number;
            }
        }
        return null;
    }

    // Gets the URI for the source or returns null if it is an invalid URI
    public static URI getSourceURI(String sourceURI) {
        try {
            if (sourceURI != null) {
                URI uri = new URI(sourceURI);
                return uri;
            }
        } catch (URISyntaxException e) {
            // Do nothing
        }
        return null;
    }

    public static String getInstance(String url) {
        if (url != null) {
            String instanceID = url.replaceAll("^https://", "");

            if (instanceID.endsWith("/")) {
                instanceID = instanceID.substring(0, instanceID.length() - 1);
            }
            return instanceID;
        }
        return url;
    }
}