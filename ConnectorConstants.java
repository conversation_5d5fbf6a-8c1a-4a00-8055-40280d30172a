/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import java.net.URI;
import java.util.Arrays;
import java.util.List;

public class ConnectorConstants {

    public final static String TOOL_TYPE_SNOW = "com.ibm.sdlc.type.snow";

    // Comment creation action types
    public final static String COMMENT_CREATE = "com.ibm.sdlc.snow.comment.create";
    public final static String COMMENT_CREATE_COMPLETED = "com.ibm.sdlc.snow.comment.create.completed";

    // Incident poll action types
    final static String INCIDENT_POLL = "com.ibm.sdlc.snow.incident.poll";
    final static String INCIDENT_POLL_FAILED = "com.ibm.sdlc.snow.incident.poll.failed";
    final static String INCIDENT_POLL_COMPLETED = "com.ibm.sdlc.snow.incident.poll.completed";
    final static String INCIDENT_DISC = "com.ibm.sdlc.snow.incident.discovered";

    // Alert poll action types
    final static String ALERT_POLL = "com.ibm.sdlc.snow.alert.poll";
    final static String ALERT_POLL_FAILED = "com.ibm.sdlc.snow.alert.poll.failed";
    final static String ALERT_POLL_COMPLETED = "com.ibm.sdlc.snow.alert.poll.completed";
    final static String ALERT_DISC = "com.ibm.sdlc.snow.alert.discovered";

    // Change request poll action types
    final static String CHANGE_REQUEST_POLL = "com.ibm.sdlc.snow.changerequest.poll";
    final static String CHANGE_REQUEST_POLL_FAILED = "com.ibm.sdlc.snow.changerequest.poll.failed";
    final static String CHANGE_REQUEST_POLL_COMPLETED = "com.ibm.sdlc.snow.changerequest.poll.completed";
    final static String CHANGE_REQUEST_DISC = "com.ibm.sdlc.snow.changerequest.discovered";

    // Incident creation action types
    final static String INCIDENT_CREATE = "com.ibm.sdlc.snow.incident.create";
    final static String INCIDENT_CREATE_R = "com.ibm.sdlc.snow.incident.create.response";

    // Alert update action types
    final static String ALERT_UPDATE = "com.ibm.sdlc.snow.alert.update";
    final static String ALERT_UPDATE_R = "com.ibm.sdlc.snow.alert.update.response";

    // Self identifier
    static final URI SELF_SOURCE = URI.create("template.connectors.aiops.watson.ibm.com/connectorsnow");

    // Connector specific cloud event types
    static final String TEST_REQUESTED_CE_TYPE = "com.ibm.watson.aiops.connectors.template.test-requested";
    static final String TEST_COMPLETED_CE_TYPE = "com.ibm.watson.aiops.connectors.template.test-completed";
    static final String TEST_FAILED_CE_TYPE = "com.ibm.watson.aiops.connectors.template.test-failed";
    static final String INSTANCE_HIBERNATING_CE_TYPE = "com.ibm.watson.aiops.connectors.snow.hibernating";
    static final String INSTANCE_OFFLINE_CE_TYPE = "com.ibm.watson.aiops.connectors.snow.offline";
    static final String INSTANCE_UNAUTHENTICATED_CE_TYPE = "com.ibm.watson.aiops.connectors.snow.unauthenticated";
    static final String SNOW_APP_MISSING_CE_TYPE = "com.ibm.watson.aiops.connectors.snow.app-missing";
    static final List<String> ALERT_TYPES_LIST = Arrays.asList(INSTANCE_HIBERNATING_CE_TYPE, INSTANCE_OFFLINE_CE_TYPE,
            INSTANCE_UNAUTHENTICATED_CE_TYPE, SNOW_APP_MISSING_CE_TYPE);
    static final List<String> ALL_ALERTS_MINUS_IBM_APP = Arrays.asList(INSTANCE_HIBERNATING_CE_TYPE,
            INSTANCE_OFFLINE_CE_TYPE, INSTANCE_UNAUTHENTICATED_CE_TYPE);

    // Connector specific cloud event attributes
    static final String ADDRESS_CE_EXT = "address";
    static final String TIMEOUT_SECONDS_CE_EXT = "timeoutseconds";
    static final String RESPONSE_TIME_CE_EXT = "responsetime";

    // TODO: update topic

    // API paths
    static final String BASIC_TABLE_API_PATH = "/api/now/v2/table/";
    static final String INCIDENT_QUERY_API_PATH = BASIC_TABLE_API_PATH + "incident";
    static final String CHANGE_REQUEST_QUERY_API_PATH = BASIC_TABLE_API_PATH + "change_request";
    static final String SNOW_ALERT_API_PATH = BASIC_TABLE_API_PATH + "x_ibm_waiops_ai_manager_events";
    static final String INCIDENT_CREATE_PATH = "/api/x_ibm_waiops/aimanagerstory";
    static final String CHANGE_RISK_ASSESSMENT = "/api/x_ibm_waiops/crassessment/aiops/change_risk_assessment";

    // Source identifiers
    static final String SOURCE_IDENTIFIER_CHANGE_REQUEST = "/change_request.do?sysparm_query=number=";
    static final String SOURCE_IDENTIFIER_INCIDENT = "/incident.do?sysparm_query=number=";
    static final String SOURCE_IDENTIFIER_INCIDENT_CREATION = "/nav_to.do?uri=incident.do?sysparm_view=watson_aiops%26sys_id=";
    // keys
    static final String SYS_ID = "sys_id";
    static final String NUMBER = "number";
    static final String RAW_DATA = "raw_data";
    static final String WORK_NOTES = "work_notes";
    static final String SOURCE = "source";
    static final String TOOL_TYPE = "tooltype";
    static final String CONNECTION_ID = "connection_id";
    static final String INSTANCE = "instance";

    // units
    static final String MINUTES = "minutes";
    static final String SECONDS = "seconds";

    // counters
    static final String ACTION_POLL_COUNTER = "snow.incident.poll.action";
    static final String ACTION_POLL_ERROR_COUNTER = "snow.incident.poll.action.error";
    static final String ACTION_INCIDENT_CREATE_COUNTER = "snow.incident.create.action";
    static final String ACTION_INCIDENT_CREATE_ERROR_COUNTER = "snow.incident.create.action.error";
    static final String ACTION_ALERT_UPDATE_COUNTER = "snow.alert.update.action";
    static final String ACTION_ALERT_UPDATE_ERROR_COUNTER = "snow.alert.update.action.error";
    static final String ACTION_ALERT_POLL_COUNTER = "snow.alert.poll.action";
    static final String ACTION_ALERT_POLL_ERROR_COUNTER = "snow.alert.poll.action.error";
    static final String ACTION_COMMENT_CREATE_COUNTER = "snow.comment.create.action";
    static final String ACTION_COMMENT_CREATE_ERROR_COUNTER = "snow.comment.create.action.error";
    static final String ACTION_INCIDENT_POLL_COUNTER = "snow.incident.poll.action";
    static final String ACTION_INCIDENT_POLL_ERROR_COUNTER = "snow.incident.poll.action.error";

    // ce topics
    static final String TOPIC_INPUT_ACTION_SNOW = "cp4waiops-cartridge.connector-snow-actions";
    static final String TOPIC_OUTPUT_ACTION_SNOW = "cp4waiops-cartridge.connector-snow-actions";
    static final String TOPIC_INPUT_LIFECYCLE_EVENTS = "cp4waiops-cartridge.lifecycle.input.events";
    static final String TOPIC_OUTPUT_INCIDENT = "cp4waiops-cartridge.incident";
    static final String TOPIC_OUTPUT_SNOW_ALERT = "cp4waiops-cartridge.snow-alerts";
    static final String TOPIC_OUTPUT_CHANGE_REQUEST = "cp4waiops-cartridge.changerequest";
    static final String TOPIC_OUTPUT_ITSM_INCIDENT_RESPONSE = "cp4waiops-cartridge.itsmincidentresponse";
    static final String TOPIC_INPUT_SNOW_HANDLERS = "cp4waiops-cartridge.snow-handlers";

    static final String ACTION_CHANGE_REQUEST_POLL_COUNTER = "snow.changerequest.poll.action";
    static final String ACTION_CHANGE_REQUEST_POLL_ERROR_COUNTER = "snow.changerequest.poll.action.error";

    static final String CE_CONNECTION_MODE = "connectionmode";
    static final String HISTORICAL = "historical";
    static final String LIVE = "live";
    static final String CHANGE_REQUEST = "change_request";
    static final String INCIDENT = "incident";
    static final String INCIDENTS_SYNC_FROM_SNOW_TO_AIOPS = "incidents_sync_to_aiops";

    // Structured mode for Cloud Events
    static final String CE_EXT_STRUCTURED_CONTENT_MODE = "structuredcontentmode";
    static final String CE_EXT_STRUCTURED_CONTENT_MODE_VALUE = "true";

    static final String ENCRYPTED_PREFIX = "encrypted:";
    static final String ENV_JWT_CERT_PATH = "grpc-bridge.client-private-key-file";
    // If the environment variable is not set, it defaults to this path.
    // The Snow connector that is deployed will have the certificates added into the image
    static final String DEFAULT_PASSWORD_DECODE_CERT_PATH = "/bindings/grpc-bridge/tls.key";

    static final String QUERY_EXCLUDE_USER = "%5Esys_updated_by!=";
    static final String QUERY_STATE_CLOSED = "&state=7"; // 7 represents closed state in Servicenow
    static final String QUERY_GENERAL_PROPERTIES = "?sysparm_display_value=true&sysparm_exclude_reference_link=true";
    static final String QUERY_OFFSET = "&sysparm_offset=";
    static final String QUERY_UPDATED_BETWEEN = "&sysparm_query=sys_updated_onBETWEEN";

    // Set to a smaller paging size to prevent OOM issue when it was at 1000
    // TODO: move to secret or config map so it's configurable
    static final String QUERY_PAGING = "&sysparm_limit=100";

    static final String PAYLOAD_MAX_SIZE = "PAYLOAD_MAX_SIZE";

    static final String REQUEST_ACTION_DATA_FLOW_ON = "enable";
}