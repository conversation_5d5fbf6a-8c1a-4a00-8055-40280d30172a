/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.util.Base64;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.ibm.cp4waiops.connectors.sdk.HConnectionClient;

public class HttpClientUtil {

    static final Logger logger = Logger.getLogger(HttpClientUtil.class.getName());

    private HttpClient httpClient;
    private HttpRequest request;
    private String auth;
    private String url;

    private HConnectionClient hConnectionClient = new HConnectionClient();

    public HttpClientUtil(String url, String username, String password) {
        httpClient = HttpClient.newHttpClient();
        this.url = url;
        String rawAuth = username + ":" + password;
        auth = Base64.getEncoder().encodeToString(rawAuth.getBytes());
    }

    /**
     * Method to set the SSL Context by importing truststore certificate and to configuare proxy if enabled
     *
     * @param newrelicConfiguration
     *            - ServiceN Configuration
     *
     * @throws KeyStoreException
     *             - Exception while setting ssl
     * @throws IOException
     *             - Exception while setting ssl
     * @throws CertificateException
     *             - Exception while setting ssl
     * @throws UnrecoverableKeyException
     *             - Exception while setting ssl
     * @throws KeyManagementException
     *             - Exception while setting ssl
     * @throws NoSuchAlgorithmException
     *             - Exception while setting ssl
     *
     */
    public void setupSSLAndProxy(ConnectorConfiguration snowConfiguration, String truststorePass)
            throws KeyStoreException, IOException, CertificateException, UnrecoverableKeyException,
            KeyManagementException, NoSuchAlgorithmException {

        // Setting up HTTP Client
        hConnectionClient.setProtocol(HttpClient.Version.HTTP_1_1);
        hConnectionClient.setConnectTimeout(60);

        if (snowConfiguration.getTls()) {
            hConnectionClient.setTLS(true, truststorePass);
        } else {
            hConnectionClient.setTLS(false, truststorePass);
        }

        if (snowConfiguration.getIsProxyEnabled()) {
            logger.info("Setup HTTP client with proxy");
            hConnectionClient.setProxyHost(snowConfiguration.getProxyUrl());
            hConnectionClient.setProxyPort(Integer.toString(snowConfiguration.getProxyPort()));
            hConnectionClient.setProxy(true);

            if (snowConfiguration.getProxyUsername() == null && snowConfiguration.getProxyPassword() == null) {

                hConnectionClient.setProxyAuthentication(false);

            } else {
                hConnectionClient.setProxyUsername(snowConfiguration.getProxyUsername());
                hConnectionClient.setProxyPassword(snowConfiguration.getProxyPassword());
                hConnectionClient.setProxyAuthentication(true);
            }

        } else {
            logger.info("Setup HTTP client without proxy");
            hConnectionClient.setProxy(false);
            hConnectionClient.setProxyAuthentication(false);
        }

        this.httpClient = hConnectionClient.setClient();

    }

    public CompletableFuture<HttpResponse<String>> post(String path, String data)
            throws IOException, InterruptedException {
        request = HttpRequest.newBuilder(URI.create(url + path)).header("accept", "application/json")
                .header("authorization", "Basic " + auth).POST(HttpRequest.BodyPublishers.ofString(data)).build();
        return send(request);
    }

    public CompletableFuture<HttpResponse<String>> patch(String path, String data)
            throws IOException, InterruptedException {
        request = HttpRequest.newBuilder(URI.create(url + path)).header("accept", "application/json")
                .header("authorization", "Basic " + auth).method("PATCH", HttpRequest.BodyPublishers.ofString(data))
                .build();
        return send(request);
    }

    public CompletableFuture<HttpResponse<String>> get(String path) throws IOException, InterruptedException {
        request = HttpRequest.newBuilder(URI.create(url + path)).header("accept", "application/json")
                .header("authorization", "Basic " + auth).GET().build();
        return send(request);
    }

    private CompletableFuture<HttpResponse<String>> send(HttpRequest request) throws IOException, InterruptedException {
        return httpClient.sendAsync(request, HttpResponse.BodyHandlers.ofString());
    }

}
