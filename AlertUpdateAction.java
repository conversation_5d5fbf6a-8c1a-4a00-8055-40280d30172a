/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2024 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import java.net.http.HttpResponse;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.json.JSONObject;

import io.cloudevents.CloudEvent;
import io.micrometer.core.instrument.Counter;

public class AlertUpdateAction implements Runnable {
    ConnectorAction action;

    static final Logger logger = Logger.getLogger(AlertUpdateAction.class.getName());

    public AlertUpdateAction(ConnectorAction action) {
        this.action = action;
    }

    public void run() {
        logger.log(Level.INFO, "Run Alert Update Action");

        Counter actionCounter = action.getActionCounter();
        Counter actionErrorCounter = action.getActionErrorCounter();

        actionCounter.increment();

        ConnectorConfiguration config = action.getConfiguration();
        ActionConfiguration actionConfig = action.getActionConfiguration();

        Connector connector = action.getConnector();

        CloudEvent ce;

        JSONObject json = new JSONObject();

        HttpClientUtil httpClient = action.getHttpClient();

        String path = ConnectorConstants.SNOW_ALERT_API_PATH;

        CompletableFuture<HttpResponse<String>> cFResp;
        HttpResponse<String> res;
        String bodyString = "";
        String sys_id = "";

        JSONObject alert = null;
        JSONObject data = null;
        String alertId = actionConfig.getAlertID();
        String snowPayload = actionConfig.getSnowPayload();

        try {
            logger.log(Level.INFO, "Updating alert " + alertId);
            // parse for the sys_id & alert json
            data = new JSONObject(snowPayload);
            alert = data.getJSONObject("alert");
            sys_id = alert.getString("snowSysId");
            path += "/" + sys_id;

            // snow patch call
            cFResp = httpClient.patch(path, alert.toString());
            logger.log(Level.INFO, "Alert update successful ");

            res = cFResp.get();
            bodyString = res.body();
            logger.log(Level.INFO, "Response body " + bodyString);

            json.put("connection_id", config.getConnectionID());
            json.put("alert_id", alertId);

            JSONObject bodyStringJSONObject = null;
            bodyStringJSONObject = new JSONObject(bodyString);

            if (bodyStringJSONObject != null) {
                // The snow_response is expected to be JSON and not a string escaped version of it
                json.put("snow_response", bodyStringJSONObject);
            }
            // emit the response from snow into the itsmresponse topic
            ce = connector.createEvent(0, ConnectorConstants.ALERT_UPDATE_R, json.toString(), null);
            connector.emitCloudEvent(ConnectorConstants.TOPIC_OUTPUT_ITSM_INCIDENT_RESPONSE, alertId, ce);
        } catch (InterruptedException e) {
            actionErrorCounter.increment();
            logger.log(Level.SEVERE, "Alert update failed ", e);
        } catch (Exception e) {
            actionErrorCounter.increment();
            logger.log(Level.SEVERE, e.getMessage(), e);
            connector.triggerAlerts(config, e, bodyString);
        }
        logger.log(Level.INFO, "Alert update successful");
        connector.clearAlerts(config, ConnectorConstants.ALERT_TYPES_LIST, connector.getTimeLastCleared());

    }
}
