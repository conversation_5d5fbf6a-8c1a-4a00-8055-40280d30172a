/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import java.io.IOException;
import java.util.logging.Logger;

import jakarta.inject.Inject;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@WebServlet(urlPatterns = "/h/*")
public class SNowConnectorHealthServlet extends HttpServlet {
    static final Logger logger = Logger.getLogger(SNowConnectorHealthServlet.class.getName());

    @Inject
    ManagerInstance instance;

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        String subPath = req.getPathInfo();
        if (subPath == null) {
            super.doGet(req, resp);
            return;
        }

        switch (req.getPathInfo()) {
        case "/live":
            instance.getConnectorManager().handleLiveRequest(req, resp);
            break;
        case "/ready":
            instance.getConnectorManager().handleReadyRequest(req, resp);
            break;
        case "/metrics":
            instance.getConnectorManager().handleMetricsRequest(req, resp);
            break;
        default:
            super.doGet(req, resp);
        }
    }
}